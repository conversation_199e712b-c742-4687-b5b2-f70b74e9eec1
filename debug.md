# AI Agent Testing Guide - Psychiatric Assessment System

## Overview
This guide provides comprehensive testing procedures for the Psychiatric Assessment System built with Streamlit and PostgreSQL. Follow these steps to systematically test all features and debug any issues.

## Prerequisites
- Python environment with required packages
- PostgreSQL database server running
- Environment variables set (DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, DB_PORT)
- Streamlit app files: `streamlit_app.py` and `database.py`

## Testing Checklist

### 🔧 1. Initial Setup and Database Connection

#### Test Database Connection
```bash
# Start the Streamlit app
streamlit run streamlit_app.py

# Expected behavior:
✅ App loads without errors
✅ Sidebar shows "🟢 Database Connected" 
✅ No error messages in console
```

#### Test Database Initialization
1. Click "🔧 Initialize Database" in sidebar
2. **Expected Results:**
   - ✅ Success message: "✅ Database initialized!"
   - ✅ Console logs: "Tables created or verified successfully"
   - ✅ No error messages

#### Test Database Health Check
1. Click "🔍 Test DB Read" in sidebar
2. **Expected Results:**
   - ✅ Shows "✅ Found X patients" or "ℹ️ Database empty"
   - ✅ No connection errors

### 🗄️ 2. Database Operations Testing

#### Test Patient Data Insertion
1. Go to Assessment mode
2. Fill out Demographics section:
   - Age: 30
   - Gender: Male
   - Marital Status: Single
   - Education: Bachelor's degree
3. Click "💾 Save Progress"
4. **Expected Results:**
   - ✅ Success message with patient ID
   - ✅ Auto-save status updates
   - ✅ Data persisted in database

#### Test Patient Data Retrieval
1. Switch to Dashboard mode
2. **Expected Results:**
   - ✅ Patient list displays
   - ✅ Shows recently created patient
   - ✅ Correct data in table columns

#### Test Patient Data Loading
1. In Dashboard, select a patient
2. Click "✏️ Edit Patient"
3. Switch back to Assessment mode
4. **Expected Results:**
   - ✅ Form fields populate with existing data
   - ✅ Status shows "Patient data loaded successfully"
   - ✅ Patient ID updates in sidebar

### 🔄 3. Auto-Save System Testing

#### Test Auto-Save Configuration
1. In Assessment mode, locate Auto-Save Settings in sidebar
2. **Expected Results:**
   - ✅ Auto-save checkbox toggles correctly
   - ✅ Interval slider adjusts (30-300 seconds)
   - ✅ Status indicator updates

#### Test Change Detection
1. Enable Debug Mode
2. Modify any form field in Demographics
3. **Expected Results:**
   - ✅ "🔄 Status: Unsaved changes" appears
   - ✅ Debug panel shows "Data Changed: true"
   - ✅ Auto-save triggers after interval

#### Test Auto-Save Functionality
1. Set auto-save interval to 30 seconds
2. Make changes to patient data
3. Wait for auto-save trigger
4. **Expected Results:**
   - ✅ Status shows "Saving..." then "Auto-saved at HH:MM:SS"
   - ✅ Changes persist after page refresh
   - ✅ No UI blocking during save

### 📊 4. Dashboard Features Testing

#### Test Patient Search
1. Go to Dashboard mode
2. Enter search term in "🔍 Search patients" field
3. **Expected Results:**
   - ✅ Results filter based on search term
   - ✅ Search works for patient ID, diagnosis, gender
   - ✅ Empty search shows all results

#### Test Pagination
1. Create multiple test patients (if not available)
2. Adjust "Results per page" setting
3. Navigate through pages
4. **Expected Results:**
   - ✅ Pagination controls work correctly
   - ✅ Page numbers update
   - ✅ Results display correctly per page

#### Test Patient Actions
1. Select a patient from the list
2. Test each action button:

**View Details:**
- ✅ Shows complete patient JSON data
- ✅ Data is formatted and readable

**Edit Patient:**
- ✅ Loads patient data for editing
- ✅ Success message appears
- ✅ Prompts to switch to Assessment tab

**Export Data:**
- ✅ Download button appears
- ✅ JSON file downloads correctly
- ✅ File contains complete patient data

**Delete Patient:**
- ✅ Requires confirmation checkbox
- ✅ Successfully removes patient
- ✅ Patient disappears from list

#### Test Analytics Dashboard
1. Ensure multiple patients exist with diagnoses
2. **Expected Results:**
   - ✅ Metrics display correct counts
   - ✅ Charts render without errors
   - ✅ Diagnosis distribution shows top 10
   - ✅ Completion percentage histogram displays
   - ✅ Timeline chart shows assessment volume

### 🔍 5. Form Input and Validation Testing

#### Test Monitored Inputs
1. Go to Demographics section
2. Test each input type:

**Text Inputs:**
- ✅ Values save to session state
- ✅ Changes trigger "data changed" status
- ✅ Values persist between section navigation

**Selectboxes:**
- ✅ Options display correctly
- ✅ Selection saves properly
- ✅ Default values load from existing data

**Number Inputs:**
- ✅ Numeric validation works
- ✅ Min/max constraints enforced
- ✅ Values save as numbers, not strings

#### Test Data Validation
1. Leave required fields empty
2. Enter invalid data (e.g., age > 120)
3. Try to save
4. **Expected Results:**
   - ✅ Validation errors appear
   - ✅ Save blocked until errors resolved
   - ✅ Clear error messages displayed

### 🚫 6. Error Handling and Recovery Testing

#### Test Database Connection Loss
1. Stop PostgreSQL service temporarily
2. Try to perform database operations
3. **Expected Results:**
   - ✅ Graceful error handling
   - ✅ Offline mode activates
   - ✅ Data queued for later sync

#### Test Connection Recovery
1. Restart PostgreSQL service
2. Click "🔄 Retry Connection" in debug mode
3. **Expected Results:**
   - ✅ Connection restored
   - ✅ Status changes to "🟢 Database Connected"
   - ✅ Queued data syncs automatically

#### Test Invalid Data Handling
1. Enter malformed JSON in debug mode
2. Try to save patient with missing required fields
3. **Expected Results:**
   - ✅ Validation catches errors
   - ✅ User-friendly error messages
   - ✅ App remains stable

### 📱 7. User Interface and Navigation Testing

#### Test Section Navigation
1. Navigate through all 16 assessment sections
2. **Expected Results:**
   - ✅ Progress bar updates correctly
   - ✅ Section selector works
   - ✅ Previous/Next buttons function
   - ✅ Data persists across sections

#### Test Responsive Design
1. Resize browser window
2. Test on different screen sizes
3. **Expected Results:**
   - ✅ Layout adapts to screen size
   - ✅ Sidebar collapses appropriately
   - ✅ Charts and tables remain readable

### 🛠️ 8. Debug Mode Testing

#### Enable Debug Mode
1. Check "Enable Debug Mode" in sidebar
2. **Expected Results:**
   - ✅ Debug panel appears at bottom
   - ✅ Additional debug controls in sidebar
   - ✅ JSON data views become available

#### Test Debug Features
**Clear Cache:**
- ✅ Cache clears without errors
- ✅ App reloads fresh data

**Sync Pending Saves:**
- ✅ Shows pending save count
- ✅ Syncs data when connection restored

**Debug Information:**
- ✅ Shows current session state
- ✅ Displays connection status
- ✅ Reports validation errors

### 🔒 9. Data Security and Integrity Testing

#### Test Data Persistence
1. Create patient with complete data
2. Close browser/restart app
3. Load same patient
4. **Expected Results:**
   - ✅ All data preserved
   - ✅ No data corruption
   - ✅ Timestamps accurate

#### Test Concurrent Access
1. Open app in multiple browser tabs
2. Make changes in different tabs
3. **Expected Results:**
   - ✅ No data conflicts
   - ✅ Last save wins (expected behavior)
   - ✅ No database locks or errors

### ⚡ 10. Performance Testing

#### Test Auto-Save Performance
1. Set auto-save interval to 30 seconds
2. Make frequent changes
3. **Expected Results:**
   - ✅ UI remains responsive during saves
   - ✅ No noticeable lag or freezing
   - ✅ Save queue handles multiple requests

#### Test Large Dataset Handling
1. Create 100+ test patients
2. Test dashboard loading
3. **Expected Results:**
   - ✅ Dashboard loads within reasonable time
   - ✅ Pagination works smoothly
   - ✅ Search performs adequately

### 🐛 11. Common Issues and Debugging

#### Database Connection Issues
**Symptoms:** Red connection status, "Database not connected" errors
**Debug Steps:**
1. Check PostgreSQL service status
2. Verify environment variables
3. Check database permissions
4. Review connection logs

#### Auto-Save Not Working
**Symptoms:** "Auto-save queue full" or no auto-save triggers
**Debug Steps:**
1. Check auto-save enabled
2. Verify data changes detected
3. Check database connection
4. Review auto-save status messages

#### Form Data Not Persisting
**Symptoms:** Data disappears between sections
**Debug Steps:**
1. Check monitored input functions
2. Verify session state updates
3. Check for JavaScript errors
4. Review form key uniqueness

#### Performance Issues
**Symptoms:** Slow loading, UI freezing
**Debug Steps:**
1. Check database query performance
2. Monitor connection pool usage
3. Review auto-save queue size
4. Check for memory leaks

### 📋 12. Test Results Documentation

Use this template to document test results:

```markdown
## Test Execution Results

**Date:** [Date]
**Tester:** [Name]
**Environment:** [Development/Staging/Production]

### Database Connection Tests
- [ ] ✅ Connection established
- [ ] ✅ Database initialization successful
- [ ] ✅ Health check passes

### CRUD Operations Tests
- [ ] ✅ Patient creation works
- [ ] ✅ Patient retrieval works
- [ ] ✅ Patient update works
- [ ] ✅ Patient deletion works

### Auto-Save Tests
- [ ] ✅ Auto-save triggers correctly
- [ ] ✅ Change detection works
- [ ] ✅ Status updates properly
- [ ] ✅ Non-blocking operation confirmed

### Dashboard Tests
- [ ] ✅ Patient list displays
- [ ] ✅ Search functionality works
- [ ] ✅ Analytics charts render
- [ ] ✅ Export/import functions

### Error Handling Tests
- [ ] ✅ Connection loss handled gracefully
- [ ] ✅ Invalid data validation works
- [ ] ✅ Recovery mechanisms function

### Issues Found
[Document any bugs or issues discovered]

### Recommendations
[List any improvements or fixes needed]
```

### 🚀 13. Production Readiness Checklist

Before deploying to production:

- [ ] All tests pass successfully
- [ ] Database performance optimized
- [ ] Error logging configured
- [ ] Security measures implemented
- [ ] Backup procedures established
- [ ] User documentation completed
- [ ] Admin controls tested
- [ ] Load testing performed
- [ ] SSL/HTTPS configured
- [ ] Environment variables secured

### 📞 14. Support and Troubleshooting

**Common Commands:**
```bash
# Restart Streamlit app
streamlit run streamlit_app.py --server.port 8001
streamlit run streamlit_app.py --server.port 8000
# Check database connections
psql -h localhost -U postgres -d psychiatric_assessments

# View application logs
tail -f streamlit.log

# Clear Streamlit cache
rm -rf ~/.streamlit/
```

**Key Log Locations:**
- Application logs: Console output
- Database logs: PostgreSQL logs directory
- Error logs: Streamlit error messages

**Emergency Procedures:**
1. Database corruption: Restore from backup
2. Connection issues: Restart database service
3. App crashes: Check logs and restart
4. Data loss: Check auto-save queue and pending saves

---

## Conclusion

This testing guide covers all major features and potential failure points of the Psychiatric Assessment System. Execute these tests systematically to ensure reliability, performance, and data integrity. Document all results and issues for continuous improvement.

**Remember:** Always test in a safe environment before production deployment!