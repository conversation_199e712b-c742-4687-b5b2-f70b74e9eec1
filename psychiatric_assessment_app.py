import streamlit as st
import pandas as pd
import json
import datetime
import uuid
from typing import Dict, Any, List
import plotly.express as px
import plotly.graph_objects as go
import numpy as np
import logging
import time
from datetime import datetime, timedelta
import atexit
from functools import lru_cache
import re
from database import PsychiatricAssessmentDB
import traceback
import asyncio

# Enhanced logging configuration
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('psychiatric_app.log', mode='a')
    ]
)

# Configure page
st.set_page_config(
    page_title="Psychiatric Assessment System",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Enhanced CSS for better UX and performance indicators
st.markdown("""
<style>
    .main-header {
        font-size: 2.8rem;
        font-weight: 800;
        color: #1e3a8a;
        text-align: center;
        margin-bottom: 2rem;
        background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #7c3aed 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .section-header {
        font-size: 1.6rem;
        font-weight: 700;
        color: #1e40af;
        border-left: 5px solid #3b82f6;
        padding-left: 1rem;
        margin: 1.5rem 0;
        background: linear-gradient(90deg, rgba(59,130,246,0.1) 0%, transparent 100%);
        padding: 1rem;
        border-radius: 0 10px 10px 0;
    }
    .subsection-header {
        font-size: 1.2rem;
        font-weight: 600;
        color: #1e40af;
        margin: 1rem 0 0.5rem 0;
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 0.3rem;
    }
    .metric-card {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        padding: 1.5rem;
        border-radius: 15px;
        color: white;
        text-align: center;
        margin: 0.5rem 0;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .risk-high { background: linear-gradient(135deg, #dc2626, #b91c1c); }
    .risk-moderate { background: linear-gradient(135deg, #ea580c, #c2410c); }
    .risk-low { background: linear-gradient(135deg, #16a34a, #15803d); }
    
    .performance-indicator {
        position: fixed;
        top: 70px;
        right: 20px;
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 8px 12px;
        font-size: 0.8rem;
        z-index: 1000;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .status-online { color: #10b981; }
    .status-offline { color: #ef4444; }
    .status-saving { color: #f59e0b; }
    
    .quick-template {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        padding: 8px 12px;
        margin: 4px;
        cursor: pointer;
        display: inline-block;
        font-size: 0.85rem;
        transition: all 0.2s ease;
    }
    .quick-template:hover {
        background: #e2e8f0;
        border-color: #cbd5e1;
    }
    
    .field-status {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-left: 8px;
    }
    .field-complete { background-color: #10b981; }
    .field-partial { background-color: #f59e0b; }
    .field-empty { background-color: #ef4444; }
    
    .progress-ring {
        transform: rotate(-90deg);
    }
    .progress-ring-fill {
        transition: stroke-dashoffset 0.35s;
    }
    
    .validation-panel {
        background: #fefbf3;
        border: 1px solid #f59e0b;
        border-radius: 8px;
        padding: 12px;
        margin: 8px 0;
    }
    
    .keyboard-shortcut {
        background: #f3f4f6;
        border: 1px solid #d1d5db;
        border-radius: 4px;
        padding: 2px 6px;
        font-family: monospace;
        font-size: 0.75rem;
    }
    
    .section-complete::after {
        content: "✓";
        color: #10b981;
        font-weight: bold;
        margin-left: 8px;
    }
    
    .stSelectbox > div > div {
        background-color: #f8fafc;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
    }
    .stButton > button {
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 0.7rem 2.5rem;
        font-weight: 700;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }
    .auto-save-indicator {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        transition: opacity 0.3s ease;
    }
</style>
""", unsafe_allow_html=True)

# Enhanced session state initialization with performance tracking
def initialize_session_state():
    """Initialize all session state variables with performance tracking"""
    defaults = {
        # Core application state
        'patient_data': {},
        'current_section': 0,
        'patient_id': f"PSY-{str(uuid.uuid4())[:8].upper()}",
        'assessment_start_time': datetime.now(),
        'db_loaded': False,
        
        # Debug and performance
        'debug': False,
        'performance_mode': True,
        'load_time_threshold': 2.0,  # seconds
        'last_render_time': 0,
        'render_count': 0,
        
        # Auto-save system
        'last_auto_save': datetime.now(),
        'auto_save_enabled': True,
        'auto_save_interval': 30,  # Reduced for better UX
        'auto_save_status': "Ready",
        'data_changed': False,
        'save_in_progress': False,
        
        # Validation and integrity
        'last_validation': datetime.now(),
        'validation_errors': [],
        'validation_warnings': [],
        'field_completion_status': {},
        'required_fields_met': 0,
        'total_required_fields': 25,  # Updated count
        
        # Database and connection
        'connection_retry_count': 0,
        'last_connection_check': datetime.now(),
        'offline_mode': False,
        'pending_saves': [],
        'db_performance_log': [],
        
        # UI and UX enhancements
        'section_states': {},
        'templates': {
            'depression': "Patient reports persistent low mood lasting [duration]. Symptoms include anhedonia, fatigue, and sleep disturbance. Functional impairment evident in work/relationships.",
            'anxiety': "Patient experiences excessive worry and physical symptoms including palpitations, shortness of breath, and restlessness. Symptoms interfere with daily activities.",
            'psychosis': "Patient reports perceptual abnormalities including [type] hallucinations. Thought process shows [describe]. Reality testing [intact/impaired].",
            'substance_use': "Patient acknowledges [substance] use with pattern of [frequency]. Negative consequences include [specify]. Withdrawal symptoms: [describe].",
            'trauma': "History of [type] trauma with current PTSD symptoms including re-experiencing, avoidance, and hyperarousal. Nightmares and flashbacks present.",
            'bipolar': "History of mood episodes including [manic/hypomanic/depressed]. Current mood state: [describe]. Psychotic features: [present/absent].",
            'personality': "Pervasive pattern of [specify traits] evident across contexts. Interpersonal difficulties and emotional dysregulation present.",
            'cognitive': "Cognitive concerns in domains of [specify]. Functional impact on ADLs/IADLs. Further neuropsychological testing recommended."
        },
        'quick_responses': {
            'onset': ['Gradual over months', 'Sudden onset', 'Following stressor', 'Chronic with acute exacerbation'],
            'course': ['Progressive worsening', 'Episodic', 'Stable', 'Improving with treatment'],
            'severity': ['Mild impairment', 'Moderate impairment', 'Severe impairment', 'Profound impairment'],
            'family_hx': ['No known family psychiatric history', 'Strong family history of depression', 'Family history of anxiety', 'Family history of substance use']
        },
        
        # Keyboard shortcuts
        'shortcuts_enabled': True,
        'current_shortcut_mode': None,
        
        # Assessment flow optimization
        'assessment_flow_mode': 'comprehensive',  # 'quick', 'comprehensive', 'focused'
        'priority_sections': [0, 1, 2, 8, 10],  # Critical sections for quick assessment
        'completed_sections': set(),
        'section_times': {},
        
        # Data integrity
        'data_checksum': None,
        'last_backup': datetime.now(),
        'backup_frequency': 300,  # 5 minutes
        'data_validation_level': 'standard'  # 'minimal', 'standard', 'strict'
    }
    
    for key, value in defaults.items():
        if key not in st.session_state:
            st.session_state[key] = value

initialize_session_state()

# Enhanced database connection with performance monitoring
@st.cache_resource
def get_database_connection():
    """Initialize database connection with enhanced monitoring"""
    start_time = time.time()
    try:
        db = PsychiatricAssessmentDB()
        health_status, message = db.health_check()
        
        connection_time = time.time() - start_time
        
        if health_status:
            logging.info(f"✅ Database connection established in {connection_time:.2f}s")
            if connection_time > st.session_state.load_time_threshold:
                logging.warning(f"⚠️ Slow database connection: {connection_time:.2f}s")
            return db
        else:
            logging.warning(f"⚠️ Database connection failed: {message}")
            return None
    except Exception as e:
        connection_time = time.time() - start_time
        logging.error(f"❌ Database initialization failed in {connection_time:.2f}s: {e}")
        return None

# Performance monitoring decorator
def monitor_performance(func):
    """Decorator to monitor function performance"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            if execution_time > st.session_state.load_time_threshold:
                logging.warning(f"⚠️ Slow function {func.__name__}: {execution_time:.2f}s")
            
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logging.error(f"❌ Function {func.__name__} failed in {execution_time:.2f}s: {e}")
            if st.session_state.debug:
                st.error(f"Function error: {func.__name__} - {str(e)}")
            raise
    return wrapper

# Get database connection
db = get_database_connection()

# Set offline mode if database unavailable
if not db:
    st.session_state.offline_mode = True
    logging.info("🔄 Starting in offline mode - database not available")

# Enhanced validation system
class DataValidator:
    """Enhanced data validation with field-level tracking"""
    
    def __init__(self):
        self.required_fields = {
            'demographics.age': {'type': int, 'min': 0, 'max': 120, 'critical': True},
            'demographics.gender': {'type': str, 'options': ['Male', 'Female', 'Non-binary', 'Other'], 'critical': True},
            'chief_complaint.complaint': {'type': str, 'min_length': 10, 'critical': True},
            'chief_complaint.symptom_duration': {'type': str, 'critical': True},
            'risk_assessment.current_si': {'type': str, 'critical': True},
            'mental_state_exam.appearance': {'type': list, 'critical': False},
            'mental_state_exam.mood': {'type': list, 'critical': False},
            'diagnostic_formulation.primary_diagnosis': {'type': str, 'critical': False}
        }
    
    @monitor_performance
    def validate_field(self, field_path, value, field_config):
        """Validate a single field with detailed feedback"""
        errors = []
        warnings = []
        
        if field_config.get('critical', False) and (value is None or value == ''):
            errors.append(f"Required critical field: {field_path}")
            return errors, warnings, 'empty'
        
        if value is None or value == '':
            return errors, warnings, 'empty'
        
        # Type validation
        expected_type = field_config.get('type')
        if expected_type and not isinstance(value, expected_type):
            try:
                if expected_type == int:
                    value = int(value)
                elif expected_type == float:
                    value = float(value)
            except (ValueError, TypeError):
                errors.append(f"Invalid type for {field_path}: expected {expected_type.__name__}")
                return errors, warnings, 'error'
        
        # Range validation for numbers
        if isinstance(value, (int, float)):
            if 'min' in field_config and value < field_config['min']:
                errors.append(f"{field_path}: value {value} below minimum {field_config['min']}")
            if 'max' in field_config and value > field_config['max']:
                warnings.append(f"{field_path}: value {value} above typical maximum {field_config['max']}")
        
        # Length validation for strings
        if isinstance(value, str):
            if 'min_length' in field_config and len(value) < field_config['min_length']:
                warnings.append(f"{field_path}: response may be too brief ({len(value)} characters)")
            if 'max_length' in field_config and len(value) > field_config['max_length']:
                warnings.append(f"{field_path}: response may be too long ({len(value)} characters)")
        
        # Options validation
        if 'options' in field_config and value not in field_config['options']:
            warnings.append(f"{field_path}: unexpected value '{value}'")
        
        return errors, warnings, 'complete'
    
    @monitor_performance
    def validate_patient_data(self, patient_data):
        """Comprehensive validation with field-level status tracking"""
        all_errors = []
        all_warnings = []
        field_status = {}
        
        for field_path, field_config in self.required_fields.items():
            # Navigate to the field value
            keys = field_path.split('.')
            current_data = patient_data
            
            try:
                for key in keys[:-1]:
                    current_data = current_data.get(key, {})
                field_value = current_data.get(keys[-1])
                
                errors, warnings, status = self.validate_field(field_path, field_value, field_config)
                
                all_errors.extend(errors)
                all_warnings.extend(warnings)
                field_status[field_path] = status
                
            except (AttributeError, TypeError) as e:
                all_errors.append(f"Data structure error for {field_path}: {str(e)}")
                field_status[field_path] = 'error'
        
        # Update session state
        st.session_state.validation_errors = all_errors
        st.session_state.validation_warnings = all_warnings
        st.session_state.field_completion_status = field_status
        st.session_state.required_fields_met = sum(1 for status in field_status.values() if status == 'complete')
        
        return all_errors, all_warnings, field_status

# Create validator instance
validator = DataValidator()

# Enhanced form input functions with real-time validation
def create_enhanced_text_input(label, key, section_key=None, validation_config=None, placeholder="", help_text=""):
    """Enhanced text input with real-time validation and field status"""
    
    field_path = f"{section_key}.{key.split('.')[-1]}" if section_key else key
    
    # Get current value and status
    current_value = ""
    if section_key and section_key in st.session_state.patient_data:
        current_value = st.session_state.patient_data[section_key].get(key.split('.')[-1], "")
    
    # Field status indicator
    field_status = st.session_state.field_completion_status.get(field_path, 'empty')
    status_indicator = {
        'complete': '🟢',
        'partial': '🟡', 
        'empty': '🔴',
        'error': '❌'
    }.get(field_status, '⚪')
    
    # Create columns for label and status
    col1, col2 = st.columns([10, 1])
    
    with col1:
        value = st.text_input(
            f"{label}", 
            value=current_value, 
            key=f"input_{key}",
            placeholder=placeholder,
            help=help_text
        )
    
    with col2:
        st.markdown(f"{status_indicator}", help=f"Field status: {field_status}")
    
    # Update patient data if changed
    if section_key:
        if section_key not in st.session_state.patient_data:
            st.session_state.patient_data[section_key] = {}
        
        field_key = key.split('.')[-1]
        if st.session_state.patient_data[section_key].get(field_key) != value:
            st.session_state.patient_data[section_key][field_key] = value
            mark_data_changed()
            
            # Real-time validation
            if validation_config:
                errors, warnings, status = validator.validate_field(field_path, value, validation_config)
                st.session_state.field_completion_status[field_path] = status
                
                if errors:
                    st.error(f"⚠️ {'; '.join(errors)}")
                elif warnings:
                    st.warning(f"⚠️ {'; '.join(warnings)}")
    
    return value

def create_enhanced_selectbox(label, options, key, section_key=None, help_text=""):
    """Enhanced selectbox with field status tracking"""
    
    field_path = f"{section_key}.{key.split('.')[-1]}" if section_key else key
    
    # Get current value
    current_value = options[0] if options else None
    if section_key and section_key in st.session_state.patient_data:
        stored_value = st.session_state.patient_data[section_key].get(key.split('.')[-1])
        if stored_value in options:
            current_value = stored_value
    
    # Field status
    field_status = st.session_state.field_completion_status.get(field_path, 'empty')
    status_indicator = {
        'complete': '🟢',
        'partial': '🟡',
        'empty': '🔴',
        'error': '❌'
    }.get(field_status, '⚪')
    
    col1, col2 = st.columns([10, 1])
    
    with col1:
        value = st.selectbox(
            label, 
            options, 
            index=options.index(current_value) if current_value in options else 0,
            key=f"select_{key}",
            help=help_text
        )
    
    with col2:
        st.markdown(f"{status_indicator}", help=f"Field status: {field_status}")
    
    # Update patient data
    if section_key:
        if section_key not in st.session_state.patient_data:
            st.session_state.patient_data[section_key] = {}
        
        field_key = key.split('.')[-1]
        if st.session_state.patient_data[section_key].get(field_key) != value:
            st.session_state.patient_data[section_key][field_key] = value
            mark_data_changed()
    
    return value

def create_enhanced_text_area(label, key, section_key=None, height=100, placeholder="", template_options=None):
    """Enhanced text area with templates and auto-completion"""
    
    field_path = f"{section_key}.{key.split('.')[-1]}" if section_key else key
    
    # Get current value
    current_value = ""
    if section_key and section_key in st.session_state.patient_data:
        current_value = st.session_state.patient_data[section_key].get(key.split('.')[-1], "")
    
    # Field status
    field_status = st.session_state.field_completion_status.get(field_path, 'empty')
    status_indicator = {
        'complete': '🟢',
        'partial': '🟡',
        'empty': '🔴',
        'error': '❌'
    }.get(field_status, '⚪')
    
    # Template buttons
    if template_options:
        st.markdown("**Quick Templates:**")
        template_cols = st.columns(len(template_options))
        
        for i, (template_key, template_text) in enumerate(template_options.items()):
            with template_cols[i]:
                if st.button(f"📝 {template_key.title()}", key=f"template_{key}_{template_key}"):
                    if current_value:
                        new_value = current_value + "\n\n" + template_text
                    else:
                        new_value = template_text
                    
                    if section_key not in st.session_state.patient_data:
                        st.session_state.patient_data[section_key] = {}
                    
                    st.session_state.patient_data[section_key][key.split('.')[-1]] = new_value
                    st.rerun()
    
    col1, col2 = st.columns([10, 1])
    
    with col1:
        value = st.text_area(
            label,
            value=current_value,
            height=height,
            key=f"textarea_{key}",
            placeholder=placeholder
        )
    
    with col2:
        st.markdown(f"{status_indicator}", help=f"Field status: {field_status}")
        if value:
            word_count = len(value.split())
            st.markdown(f"📝 {word_count} words")
    
    # Update patient data
    if section_key:
        if section_key not in st.session_state.patient_data:
            st.session_state.patient_data[section_key] = {}
        
        field_key = key.split('.')[-1]
        if st.session_state.patient_data[section_key].get(field_key) != value:
            st.session_state.patient_data[section_key][field_key] = value
            mark_data_changed()
    
    return value

def mark_data_changed():
    """Mark that data has been modified with timestamp"""
    st.session_state.data_changed = True
    st.session_state.last_validation = datetime.now()
    
    # Run validation in background
    if st.session_state.data_validation_level != 'minimal':
        validator.validate_patient_data(st.session_state.patient_data)

# Enhanced auto-save with progress indication
@monitor_performance
def enhanced_auto_save():
    """Enhanced auto-save with better user feedback"""
    if not db or st.session_state.offline_mode or st.session_state.save_in_progress:
        return False
    
    if not st.session_state.data_changed:
        return False
    
    try:
        st.session_state.save_in_progress = True
        st.session_state.auto_save_status = "Saving..."
        
        export_data = prepare_export_data()
        
        # Quick validation
        errors, warnings, field_status = validator.validate_patient_data(export_data.get('clinical_data', {}))
        
        if errors and st.session_state.data_validation_level == 'strict':
            st.session_state.auto_save_status = f"Validation errors: {len(errors)}"
            st.session_state.save_in_progress = False
            return False
        
        # Perform save
        success = db.queue_auto_save(export_data, auto_save_callback)
        
        if success:
            st.session_state.auto_save_status = "Queued for save"
        else:
            st.session_state.auto_save_status = "Save queue full"
        
        st.session_state.save_in_progress = False
        return success
        
    except Exception as e:
        st.session_state.auto_save_status = f"Save error: {str(e)[:50]}..."
        st.session_state.save_in_progress = False
        logging.error(f"Enhanced auto-save error: {e}")
        return False

def auto_save_callback(success, patient_id, error):
    """Enhanced callback with better status tracking"""
    if success:
        st.session_state.auto_save_status = f"✅ Saved at {datetime.now().strftime('%H:%M:%S')}"
        st.session_state.last_auto_save = datetime.now()
        st.session_state.data_changed = False
        logging.info(f"Auto-save successful for {patient_id}")
    else:
        st.session_state.auto_save_status = f"❌ Save failed: {error[:30]}..."
        logging.error(f"Auto-save failed: {error}")

def prepare_export_data():
    """Prepare data for export with enhanced metadata"""
    return {
        'metadata': {
            'patient_id': st.session_state.patient_id,
            'assessment_date': datetime.now().isoformat(),
            'duration_minutes': (datetime.now() - st.session_state.assessment_start_time).total_seconds() / 60,
            'completion_percentage': len([k for k in st.session_state.patient_data.keys() if st.session_state.patient_data[k]]) / 16 * 100,
            'data_version': '2.0',
            'export_timestamp': datetime.now().isoformat(),
            'validation_level': st.session_state.data_validation_level,
            'required_fields_met': st.session_state.required_fields_met,
            'total_required_fields': st.session_state.total_required_fields,
            'assessment_flow_mode': st.session_state.assessment_flow_mode,
            'render_performance': {
                'render_count': st.session_state.render_count,
                'average_render_time': st.session_state.last_render_time,
                'load_time_threshold': st.session_state.load_time_threshold
            }
        },
        'clinical_data': st.session_state.patient_data,
        'field_completion': st.session_state.field_completion_status,
        'validation_results': {
            'errors': st.session_state.validation_errors,
            'warnings': st.session_state.validation_warnings,
            'last_validation': st.session_state.last_validation.isoformat()
        }
    }

# Performance monitoring display
def show_performance_indicator():
    """Show real-time performance indicator"""
    if st.session_state.performance_mode:
        connection_status = "🟢 Online" if db and not st.session_state.offline_mode else "🔴 Offline"
        save_status = "💾 Saving" if st.session_state.save_in_progress else ("💾 Saved" if not st.session_state.data_changed else "💾 Unsaved")
        
        completion_pct = (st.session_state.required_fields_met / st.session_state.total_required_fields) * 100
        
        st.markdown(f'''
        <div class="performance-indicator">
            <div>{connection_status} | {save_status}</div>
            <div>📊 {completion_pct:.0f}% Complete</div>
            <div>⏱️ Renders: {st.session_state.render_count}</div>
        </div>
        ''', unsafe_allow_html=True)

# Quick assessment flow
def show_quick_assessment():
    """Streamlined quick assessment for urgent cases"""
    st.markdown('<div class="section-header">⚡ Quick Assessment Mode</div>', unsafe_allow_html=True)
    
    st.info("🚀 **Quick Mode**: Complete critical fields first, expand details later")
    
    # Critical fields in order of importance
    with st.form("quick_assessment_form"):
        st.markdown("### 🔴 Critical Information")
        
        col1, col2 = st.columns(2)
        
        with col1:
            age = st.number_input("Age*", min_value=0, max_value=120, value=30, key="quick_age")
            gender = st.selectbox("Gender*", ["Male", "Female", "Non-binary", "Other"], key="quick_gender")
            chief_complaint = st.text_area("Chief Complaint* (What brings you here?)", 
                                         placeholder="Patient's main concern in their own words...", 
                                         height=80, key="quick_complaint")
        
        with col2:
            urgency = st.selectbox("Urgency Level*", ["Routine", "Urgent", "Emergent"], key="quick_urgency")
            suicide_risk = st.selectbox("Suicidal Ideation*", 
                                      ["None", "Passive", "Active without plan", "Active with plan", "Imminent risk"],
                                      key="quick_suicide")
            duration = st.selectbox("Symptom Duration*", 
                                   ["< 2 weeks", "2-4 weeks", "1-3 months", "3-6 months", "6+ months"],
                                   key="quick_duration")
        
        st.markdown("### 🟡 Important Details")
        
        col3, col4 = st.columns(2)
        
        with col3:
            mood_symptoms = st.multiselect("Current Mood Symptoms",
                                         ["Depressed mood", "Anxiety", "Irritability", "Euphoria", "Mood swings"],
                                         key="quick_mood")
            substance_use = st.selectbox("Recent Substance Use", 
                                       ["None", "Alcohol", "Drugs", "Both", "Prescribed medications only"],
                                       key="quick_substance")
        
        with col4:
            psychotic_symptoms = st.multiselect("Psychotic Symptoms",
                                               ["None", "Hallucinations", "Delusions", "Paranoia", "Disorganized thinking"],
                                               key="quick_psychotic")
            previous_episodes = st.selectbox("Previous Episodes", 
                                           ["First episode", "2-3 episodes", "Multiple episodes", "Chronic"],
                                           key="quick_previous")
        
        submitted = st.form_submit_button("💾 Save Quick Assessment", use_container_width=True)
        
        if submitted:
            # Save to main patient data structure
            if 'demographics' not in st.session_state.patient_data:
                st.session_state.patient_data['demographics'] = {}
            if 'chief_complaint' not in st.session_state.patient_data:
                st.session_state.patient_data['chief_complaint'] = {}
            if 'risk_assessment' not in st.session_state.patient_data:
                st.session_state.patient_data['risk_assessment'] = {}
            if 'hpi' not in st.session_state.patient_data:
                st.session_state.patient_data['hpi'] = {}
            
            # Map quick assessment to full data structure
            st.session_state.patient_data['demographics']['age'] = age
            st.session_state.patient_data['demographics']['gender'] = gender
            st.session_state.patient_data['chief_complaint']['complaint'] = chief_complaint
            st.session_state.patient_data['chief_complaint']['urgency_level'] = urgency
            st.session_state.patient_data['chief_complaint']['symptom_duration'] = duration
            st.session_state.patient_data['risk_assessment']['current_si'] = suicide_risk
            st.session_state.patient_data['hpi']['mood_symptoms'] = mood_symptoms
            st.session_state.patient_data['hpi']['psychotic_symptoms'] = psychotic_symptoms
            st.session_state.patient_data['substance_use'] = {'current_use': substance_use}
            st.session_state.patient_data['past_psychiatric_history'] = {'previous_episodes': previous_episodes}
            
            mark_data_changed()
            st.success("✅ Quick assessment saved! You can now continue with detailed sections.")
            
            # Auto-switch to appropriate detailed section based on urgency
            if urgency == "Emergent" or suicide_risk in ["Active with plan", "Imminent risk"]:
                st.session_state.current_section = 10  # Risk Assessment
                st.warning("🚨 High-risk assessment - automatically switching to Risk Assessment section")
                time.sleep(2)
                st.rerun()
            else:
                st.session_state.current_section = 2  # History of Present Illness
                st.rerun()

# Enhanced sidebar with intelligent navigation
def show_enhanced_sidebar():
    """Enhanced sidebar with smart navigation and progress tracking"""
    with st.sidebar:
        st.markdown("### 🧠 Psychiatric Assessment System")
        
        # Connection and performance status
        connection_healthy = check_connection_health()
        if connection_healthy:
            st.markdown('<div class="connection-status-good">🟢 Database Connected</div>', unsafe_allow_html=True)
        else:
            if st.session_state.offline_mode:
                st.markdown('<div class="connection-status-bad">🔴 Offline Mode</div>', unsafe_allow_html=True)
                st.info(f"💡 Pending saves: {len(st.session_state.pending_saves)}")
            else:
                st.markdown('<div class="connection-status-bad">🟡 Connection Issues</div>', unsafe_allow_html=True)
        
        # Assessment flow mode selector
        st.markdown("---")
        flow_mode = st.selectbox("Assessment Mode", 
                                ["comprehensive", "quick", "focused"],
                                index=["comprehensive", "quick", "focused"].index(st.session_state.assessment_flow_mode))
        if flow_mode != st.session_state.assessment_flow_mode:
            st.session_state.assessment_flow_mode = flow_mode
            st.rerun()
        
        # Progress visualization
        st.markdown("#### 📊 Assessment Progress")
        completion_pct = (st.session_state.required_fields_met / st.session_state.total_required_fields) * 100
        
        progress_html = f"""
        <div style="background: #f0f2f6; border-radius: 10px; padding: 10px; margin: 10px 0;">
            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                <span>Completion</span>
                <span>{completion_pct:.0f}%</span>
            </div>
            <div style="background: #e0e0e0; border-radius: 5px; height: 8px;">
                <div style="background: linear-gradient(90deg, #10b981, #3b82f6); 
                           width: {completion_pct}%; height: 100%; border-radius: 5px;"></div>
            </div>
            <div style="font-size: 0.8em; color: #666; margin-top: 5px;">
                {st.session_state.required_fields_met}/{st.session_state.total_required_fields} critical fields
            </div>
        </div>
        """
        st.markdown(progress_html, unsafe_allow_html=True)
        
        # Smart section navigation
        st.markdown("#### 🧭 Smart Navigation")
        
        sections = [
            "Demographics & Identifying Information",
            "Chief Complaint & Referral", 
            "History of Present Illness",
            "Past Psychiatric History",
            "Past Medical History",
            "Family History",
            "Social & Developmental History", 
            "Substance Use Assessment",
            "Mental State Examination",
            "Cognitive Assessment",
            "Risk Assessment",
            "Laboratory & Investigations",
            "Clinical Scales & Ratings",
            "Diagnostic Formulation",
            "Treatment Planning",
            "Follow-up & Monitoring"
        ]
        
        # Show section completion status
        for i, section in enumerate(sections):
            section_key = section.lower().replace(' & ', '_').replace(' ', '_')
            is_complete = section_key in st.session_state.patient_data and st.session_state.patient_data[section_key]
            is_current = i == st.session_state.current_section
            
            status_icon = "✅" if is_complete else ("📝" if is_current else "⭕")
            priority_marker = "🔥" if i in st.session_state.priority_sections else ""
            
            if st.button(f"{status_icon} {priority_marker} {section}", 
                        key=f"nav_{i}",
                        help=f"{'Current section' if is_current else ('Completed' if is_complete else 'Not started')}"):
                st.session_state.current_section = i
                st.rerun()
        
        # Quick actions
        st.markdown("---")
        st.markdown("#### ⚡ Quick Actions")
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("💾 Save Now", help="Manual save"):
                save_patient_to_db(is_manual=True)
            
            if st.button("🔄 Validate", help="Run validation"):
                validator.validate_patient_data(st.session_state.patient_data)
                st.success("Validation complete!")
        
        with col2:
            if st.button("📊 Analytics", help="Show analytics"):
                st.session_state.show_analytics = not st.session_state.get('show_analytics', False)
                st.rerun()
            
            if st.button("🎯 Focus Mode", help="Focus on critical sections"):
                st.session_state.current_section = st.session_state.priority_sections[0]
                st.rerun()
        
        # Auto-save status
        st.markdown("---")
        st.markdown("#### 💾 Auto-Save")
        
        st.session_state.auto_save_enabled = st.checkbox(
            "Enable Auto-Save", 
            value=st.session_state.auto_save_enabled
        )
        
        if st.session_state.auto_save_enabled:
            interval = st.slider("Interval (seconds)", 15, 180, st.session_state.auto_save_interval)
            if interval != st.session_state.auto_save_interval:
                st.session_state.auto_save_interval = interval
            
            # Status with color coding
            status = st.session_state.auto_save_status
            if "✅" in status:
                st.success(status)
            elif "❌" in status:
                st.error(status)
            elif "Saving" in status:
                st.info(f"🔄 {status}")
            else:
                st.info(status)
        
        # Debug and developer tools
        st.markdown("---")
        st.markdown("#### 🛠️ Developer Tools")
        st.session_state.debug = st.checkbox("Debug Mode", value=st.session_state.debug)
        st.session_state.performance_mode = st.checkbox("Performance Monitor", value=st.session_state.performance_mode)
        
        if st.session_state.debug:
            with st.expander("🔧 Debug Actions"):
                if st.button("Clear Cache"):
                    st.cache_resource.clear()
                    st.cache_data.clear()
                    st.success("Cache cleared!")
                
                if st.button("Reset Assessment"):
                    if st.checkbox("⚠️ Confirm Reset"):
                        # Reset all session state
                        for key in list(st.session_state.keys()):
                            if key not in ['debug', 'performance_mode']:
                                del st.session_state[key]
                        initialize_session_state()
                        st.success("Assessment reset!")
                        st.rerun()
                
                if st.button("Export Debug Data"):
                    debug_data = {
                        'session_state': {k: str(v) for k, v in st.session_state.items()},
                        'patient_data': st.session_state.patient_data,
                        'field_completion': st.session_state.field_completion_status,
                        'validation_errors': st.session_state.validation_errors,
                        'performance_log': st.session_state.get('db_performance_log', [])
                    }
                    st.download_button(
                        "📥 Download Debug Data",
                        data=json.dumps(debug_data, indent=2, default=str),
                        file_name=f"debug_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                        mime="application/json"
                    )

# Enhanced connection health check
def check_connection_health():
    """Enhanced connection health check with retry logic"""
    current_time = datetime.now()
    
    # Only check every 30 seconds to avoid overhead
    if (current_time - st.session_state.last_connection_check).seconds < 30:
        return db is not None and not st.session_state.offline_mode
    
    st.session_state.last_connection_check = current_time
    
    if not db:
        st.session_state.offline_mode = True
        return False
    
    try:
        health_status, message = db.health_check()
        if not health_status:
            st.session_state.connection_retry_count += 1
            if st.session_state.connection_retry_count > 3:
                st.session_state.offline_mode = True
                if st.session_state.debug:
                    st.warning(f"⚠️ Database health check failed: {message}")
            return False
        else:
            st.session_state.connection_retry_count = 0
            if st.session_state.offline_mode:
                st.session_state.offline_mode = False
                st.success("🟢 Database connection restored!")
            return True
    except Exception as e:
        st.session_state.connection_retry_count += 1
        if st.session_state.debug:
            st.error(f"Connection check error: {str(e)}")
        return False

# Enhanced save function with better error handling
@monitor_performance
def save_patient_to_db(is_manual=True):
    """Enhanced save with comprehensive error handling"""
    if not db or st.session_state.offline_mode:
        if is_manual:
            st.error("❌ Database not connected. Cannot save patient data.")
            st.info("💡 Data is being cached locally for when connection is restored.")
        return False
    
    try:
        export_data = prepare_export_data()
        
        # Enhanced validation
        errors, warnings, field_status = validator.validate_patient_data(export_data.get('clinical_data', {}))
        
        if errors:
            if st.session_state.data_validation_level == 'strict' and is_manual:
                st.error("❌ Critical validation errors found:")
                for error in errors:
                    st.error(f"• {error}")
                return False
            elif is_manual:
                st.warning("⚠️ Validation warnings (data saved with warnings):")
                for error in errors[:5]:  # Limit display
                    st.warning(f"• {error}")
                if len(errors) > 5:
                    st.warning(f"... and {len(errors) - 5} more errors")
        
        if warnings and is_manual and st.session_state.data_validation_level != 'minimal':
            with st.expander("⚠️ Data Quality Warnings"):
                for warning in warnings:
                    st.warning(f"• {warning}")
        
        # Perform save
        if is_manual:
            patient_id = db.insert_patient_data(export_data)
            st.success(f"✅ Assessment saved successfully!")
            st.info(f"📋 Patient ID: `{patient_id}`")
            
            # Update session state
            st.session_state.last_auto_save = datetime.now()
            st.session_state.data_changed = False
            st.session_state.auto_save_status = f"✅ Manually saved at {datetime.now().strftime('%H:%M:%S')}"
            
            # Show save summary
            completion_pct = (st.session_state.required_fields_met / st.session_state.total_required_fields) * 100
            st.metric("Data Completeness", f"{completion_pct:.1f}%", 
                     f"{st.session_state.required_fields_met}/{st.session_state.total_required_fields} fields")
        else:
            # Auto-save
            db.queue_auto_save(export_data, auto_save_callback)
        
        return True
        
    except Exception as e:
        error_msg = f"❌ Save operation failed: {str(e)}"
        if is_manual:
            st.error(error_msg)
            st.error("🔧 Please check your database connection and try again.")
            
            # Offer to save offline
            if st.button("💾 Save Offline (Local Cache)"):
                st.session_state.pending_saves.append({
                    'data': export_data,
                    'timestamp': datetime.now().isoformat(),
                    'manual': is_manual
                })
                st.success("✅ Data cached offline. Will sync when database is available.")
        else:
            st.session_state.auto_save_status = f"❌ Save failed: {str(e)[:30]}..."
        
        if st.session_state.debug:
            st.exception(e)
        
        logging.error(f"Save operation failed: {e}")
        return False

# Load patient function with error handling
@monitor_performance
def load_patient_from_db(patient_id):
    """Enhanced patient loading with validation"""
    if not db or st.session_state.offline_mode:
        st.error("❌ Database not connected. Cannot load patient data.")
        return False
    
    try:
        with st.spinner(f"🔄 Loading patient {patient_id}..."):
            patient_data = db.get_patient_data(patient_id)
            
            if patient_data:
                # Load clinical data
                st.session_state.patient_data = patient_data.get('clinical_data', {})
                st.session_state.patient_id = patient_id
                st.session_state.db_loaded = True
                st.session_state.data_changed = False
                
                # Load metadata if available
                if 'metadata' in patient_data:
                    metadata = patient_data['metadata']
                    if metadata.get('assessment_date'):
                        try:
                            st.session_state.assessment_start_time = datetime.fromisoformat(
                                metadata['assessment_date'].replace('Z', '+00:00')
                            )
                        except:
                            pass
                    
                    # Load field completion status if available
                    if 'field_completion' in patient_data:
                        st.session_state.field_completion_status = patient_data['field_completion']
                
                # Run validation on loaded data
                validator.validate_patient_data(st.session_state.patient_data)
                
                st.success(f"✅ Patient {patient_id} loaded successfully!")
                
                # Show load summary
                completion_pct = (st.session_state.required_fields_met / st.session_state.total_required_fields) * 100
                st.info(f"📊 Data completeness: {completion_pct:.1f}% ({st.session_state.required_fields_met}/{st.session_state.total_required_fields} fields)")
                
                if st.session_state.validation_errors:
                    st.warning(f"⚠️ Found {len(st.session_state.validation_errors)} validation issues")
                
                return True
            else:
                st.warning(f"⚠️ No data found for patient ID: {patient_id}")
                return False
                
    except Exception as e:
        st.error(f"❌ Error loading patient data: {str(e)}")
        if st.session_state.debug:
            st.exception(e)
        logging.error(f"Patient load error: {e}")
        return False

# Auto-save checker with improved logic
def check_auto_save():
    """Enhanced auto-save checker"""
    if not st.session_state.auto_save_enabled or st.session_state.save_in_progress:
        return
    
    now = datetime.now()
    time_since_last_save = (now - st.session_state.last_auto_save).total_seconds()
    
    # Auto-save conditions
    should_save = (
        st.session_state.data_changed and
        time_since_last_save >= st.session_state.auto_save_interval
    )
    
    if should_save:
        enhanced_auto_save()

# Main application logic
def main():
    """Main application with enhanced flow control"""
    start_render = time.time()
    
    # Performance indicator
    show_performance_indicator()
    
    # Enhanced sidebar
    show_enhanced_sidebar()
    
    # Main header
    st.markdown('<h1 class="main-header">🧠 Comprehensive Psychiatric Assessment System</h1>', unsafe_allow_html=True)
    
    # Check auto-save
    check_auto_save()
    
    # Assessment flow based on mode
    if st.session_state.assessment_flow_mode == 'quick':
        show_quick_assessment()
    else:
        show_comprehensive_assessment()
    
    # Update performance metrics
    render_time = time.time() - start_render
    st.session_state.last_render_time = render_time
    st.session_state.render_count += 1
    
    if render_time > st.session_state.load_time_threshold:
        logging.warning(f"⚠️ Slow page render: {render_time:.2f}s")

def show_comprehensive_assessment():
    """Show comprehensive assessment form"""
    sections = [
        "Demographics & Identifying Information",
        "Chief Complaint & Referral",
        "History of Present Illness", 
        "Past Psychiatric History",
        "Past Medical History",
        "Family History",
        "Social & Developmental History",
        "Substance Use Assessment", 
        "Mental State Examination",
        "Cognitive Assessment",
        "Risk Assessment",
        "Laboratory & Investigations",
        "Clinical Scales & Ratings",
        "Diagnostic Formulation",
        "Treatment Planning",
        "Follow-up & Monitoring"
    ]
    
    current_section_name = sections[st.session_state.current_section]
    
    # Section header with completion status
    section_key = current_section_name.lower().replace(' & ', '_').replace(' ', '_')
    is_complete = section_key in st.session_state.patient_data and st.session_state.patient_data[section_key]
    completion_icon = "✅" if is_complete else "📝"
    
    st.markdown(f'<div class="section-header">{completion_icon} {current_section_name}</div>', unsafe_allow_html=True)
    
    # Show current section form
    if current_section_name == "Demographics & Identifying Information":
        show_demographics_section()
    elif current_section_name == "Chief Complaint & Referral":
        show_chief_complaint_section()
    elif current_section_name == "History of Present Illness":
        show_hpi_section()
    elif current_section_name == "Risk Assessment":
        show_risk_assessment_section()
    elif current_section_name == "Mental State Examination":
        show_mse_section()
    else:
        show_section_placeholder(current_section_name)
    
    # Navigation controls
    show_navigation_controls(sections)

def show_demographics_section():
    """Enhanced demographics section with validation"""
    st.markdown("### 👤 Patient Demographics")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("#### Basic Information")
        age = create_enhanced_text_input(
            "Age*", "demographics.age", "demographics",
            validation_config={'type': int, 'min': 0, 'max': 120, 'critical': True},
            help_text="Patient's current age in years"
        )
        
        gender = create_enhanced_selectbox(
            "Gender Identity*",
            ["Male", "Female", "Non-binary", "Transgender male", "Transgender female", "Other", "Prefer not to disclose"],
            "demographics.gender", "demographics",
            help_text="Patient's self-identified gender"
        )
        
        marital_status = create_enhanced_selectbox(
            "Marital Status",
            ["Single, never married", "Married", "Divorced", "Widowed", "Separated", "Cohabiting", "Other"],
            "demographics.marital_status", "demographics"
        )
    
    with col2:
        st.markdown("#### Socioeconomic")
        education = create_enhanced_selectbox(
            "Education Level",
            ["Less than high school", "High school/GED", "Some college", "Associate degree", 
             "Bachelor's degree", "Master's degree", "Doctoral degree"],
            "demographics.education", "demographics"
        )
        
        occupation = create_enhanced_text_input(
            "Occupation", "demographics.occupation", "demographics",
            placeholder="Current or most recent job title"
        )
        
        employment_status = create_enhanced_selectbox(
            "Employment Status", 
            ["Employed full-time", "Employed part-time", "Unemployed", "Student", "Retired", "Disabled"],
            "demographics.employment_status", "demographics"
        )
    
    with col3:
        st.markdown("#### Cultural Background")
        ethnicity = st.multiselect(
            "Race/Ethnicity",
            ["White/Caucasian", "Black/African American", "Hispanic/Latino", "Asian", 
             "Native American", "Pacific Islander", "Mixed race", "Other"],
            key="demo_ethnicity"
        )
        
        primary_language = create_enhanced_selectbox(
            "Primary Language",
            ["English", "Spanish", "French", "Chinese", "Arabic", "Other"],
            "demographics.primary_language", "demographics"
        )
        
        religion = create_enhanced_selectbox(
            "Religious/Spiritual",
            ["Christian", "Muslim", "Jewish", "Hindu", "Buddhist", "Agnostic", "Atheist", "Other", "Prefer not to disclose"],
            "demographics.religion", "demographics"
        )

def show_chief_complaint_section():
    """Enhanced chief complaint section"""
    st.markdown("### 🗣️ Chief Complaint & Referral")
    
    # Main complaint with templates
    template_options = {
        'depression': st.session_state.templates['depression'],
        'anxiety': st.session_state.templates['anxiety'],
        'psychosis': st.session_state.templates['psychosis']
    }
    
    complaint = create_enhanced_text_area(
        "Chief Complaint* (in patient's own words)",
        "chief_complaint.complaint", "chief_complaint",
        height=120,
        placeholder="What brings the patient in today? Use their exact words when possible...",
        template_options=template_options
    )
    
    col1, col2 = st.columns(2)
    
    with col1:
        duration = create_enhanced_selectbox(
            "Symptom Duration*",
            ["< 2 weeks", "2-4 weeks", "1-3 months", "3-6 months", "6-12 months", "> 1 year"],
            "chief_complaint.symptom_duration", "chief_complaint"
        )
        
        onset = create_enhanced_selectbox(
            "Symptom Onset",
            ["Acute (sudden)", "Gradual", "Chronic with acute exacerbation"],
            "chief_complaint.symptom_onset", "chief_complaint"
        )
        
        severity = st.slider(
            "Symptom Severity (1-10)", 1, 10, 5,
            help="1 = Minimal impact, 10 = Severe impairment",
            key="complaint_severity"
        )
    
    with col2:
        referral_source = create_enhanced_selectbox(
            "Referral Source",
            ["Self-referral", "Family", "Primary care", "Emergency dept", "Other specialist", "Court-ordered"],
            "chief_complaint.referral_source", "chief_complaint"
        )
        
        urgency = create_enhanced_selectbox(
            "Urgency Level*",
            ["Routine", "Urgent", "Emergent"],
            "chief_complaint.urgency_level", "chief_complaint"
        )
        
        previous_treatment = create_enhanced_selectbox(
            "Previous Treatment",
            ["None", "Counseling only", "Medication only", "Both", "Hospitalization", "Multiple providers"],
            "chief_complaint.previous_treatment", "chief_complaint"
        )

def show_hpi_section():
    """History of Present Illness section with timeline"""
    st.markdown("### 📋 History of Present Illness")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### Timeline & Course")
        first_episode = st.date_input(
            "Date of First Episode",
            help="When did symptoms first begin?",
            key="hpi_first_episode"
        )
        
        current_episode_start = st.date_input(
            "Current Episode Start",
            help="When did current symptoms begin/worsen?", 
            key="hpi_current_start"
        )
        
        progression = create_enhanced_selectbox(
            "Symptom Progression",
            ["Gradually worsening", "Stable", "Fluctuating", "Episodic", "Improving"],
            "hpi.symptom_progression", "hpi"
        )
    
    with col2:
        st.markdown("#### Contributing Factors")
        triggers = create_enhanced_text_area(
            "Triggers/Precipitants",
            "hpi.trigger_events", "hpi",
            height=80,
            placeholder="Life events, stressors, changes that preceded symptoms..."
        )
        
        relieving_factors = create_enhanced_text_area(
            "Relieving Factors",
            "hpi.relieving_factors", "hpi", 
            height=80,
            placeholder="What helps? Medications, activities, situations..."
        )
    
    # Symptom domains with quick checkboxes
    st.markdown("#### Current Symptoms")
    
    tab1, tab2, tab3, tab4 = st.tabs(["Mood", "Anxiety", "Psychotic", "Cognitive"])
    
    with tab1:
        mood_symptoms = st.multiselect(
            "Mood Symptoms",
            ["Depressed mood", "Anhedonia", "Mood swings", "Irritability", "Euphoria", "Dysthymia"],
            key="hpi_mood_symptoms"
        )
        
        mood_duration = st.selectbox(
            "Mood Episode Duration",
            ["< 2 weeks", "2+ weeks", "2+ months", "2+ years", "Chronic"],
            key="hpi_mood_duration"
        )
    
    with tab2:
        anxiety_symptoms = st.multiselect(
            "Anxiety Symptoms", 
            ["Excessive worry", "Panic attacks", "Social anxiety", "Phobias", "Compulsions", "Physical symptoms"],
            key="hpi_anxiety_symptoms"
        )
        
        anxiety_triggers = create_enhanced_text_input(
            "Anxiety Triggers", "hpi.anxiety_triggers", "hpi",
            placeholder="Specific situations, thoughts, or events..."
        )
    
    with tab3:
        psychotic_symptoms = st.multiselect(
            "Psychotic Symptoms",
            ["Auditory hallucinations", "Visual hallucinations", "Delusions", "Paranoia", "Thought disorder"],
            key="hpi_psychotic_symptoms"
        )
        
        if psychotic_symptoms:
            psychotic_details = create_enhanced_text_area(
                "Psychotic Symptom Details",
                "hpi.psychotic_details", "hpi",
                height=100,
                placeholder="Describe content, frequency, and impact of symptoms..."
            )
    
    with tab4:
        cognitive_symptoms = st.multiselect(
            "Cognitive Symptoms",
            ["Memory problems", "Concentration issues", "Confusion", "Disorientation", "Executive dysfunction"],
            key="hpi_cognitive_symptoms"
        )
        
        cognitive_impact = create_enhanced_selectbox(
            "Functional Impact",
            ["None", "Mild", "Moderate", "Severe", "Profound"],
            "hpi.cognitive_impact", "hpi"
        )

def show_risk_assessment_section():
    """Enhanced risk assessment with safety planning"""
    st.markdown("### ⚠️ Risk Assessment")
    
    # Suicide risk assessment
    with st.expander("🔴 Suicide Risk Assessment", expanded=True):
        col1, col2 = st.columns(2)
        
        with col1:
            current_si = create_enhanced_selectbox(
                "Current Suicidal Ideation*",
                ["None", "Passive (wish to be dead)", "Active without plan", "Active with plan", "Imminent risk"],
                "risk_assessment.current_si", "risk_assessment"
            )
            
            if current_si != "None":
                si_frequency = create_enhanced_selectbox(
                    "Frequency of Thoughts",
                    ["Rare", "Occasional", "Daily", "Constant"],
                    "risk_assessment.si_frequency", "risk_assessment"
                )
                
                si_intensity = st.slider(
                    "Intensity (1-10)", 1, 10, 5,
                    help="How strong are the suicidal thoughts?",
                    key="si_intensity"
                )
                
                plan_details = create_enhanced_text_area(
                    "Plan Details*",
                    "risk_assessment.plan_details", "risk_assessment",
                    height=100,
                    placeholder="Method, location, timing, preparation..."
                )
        
        with col2:
            if current_si != "None":
                means_access = create_enhanced_selectbox(
                    "Access to Lethal Means*",
                    ["No access", "Limited access", "Easy access", "Immediate access"],
                    "risk_assessment.means_access", "risk_assessment"
                )
                
                protective_factors = st.multiselect(
                    "Protective Factors",
                    ["Family responsibilities", "Religious beliefs", "Future plans", "Treatment hope", 
                     "Social support", "Pets", "Fear of death", "Children"],
                    key="protective_factors"
                )
                
                deterrents = create_enhanced_text_area(
                    "What Stops Patient?",
                    "risk_assessment.deterrents", "risk_assessment",
                    height=80,
                    placeholder="What keeps the patient from acting on thoughts?"
                )
        
        # Risk level calculation and display
        if current_si != "None":
            risk_level = calculate_suicide_risk_level(current_si, means_access)
            
            risk_colors = {
                "Low": "🟢",
                "Moderate": "🟡", 
                "High": "🔴",
                "Imminent": "🚨"
            }
            
            st.markdown(f"### {risk_colors.get(risk_level, '⚪')} **Current Risk Level: {risk_level}**")
            
            if risk_level in ["High", "Imminent"]:
                st.error("🚨 **HIGH RISK ALERT**: Immediate safety interventions required!")
                
                safety_plan = st.checkbox("Safety plan created", key="safety_plan_created")
                
                if st.button("📋 Generate Safety Plan Template"):
                    safety_plan_template = generate_safety_plan_template(protective_factors, deterrents)
                    st.text_area("Safety Plan", value=safety_plan_template, height=200, key="safety_plan_text")
    
    # Violence risk
    with st.expander("⚡ Violence Risk Assessment"):
        col1, col2 = st.columns(2)
        
        with col1:
            homicidal_ideation = create_enhanced_selectbox(
                "Homicidal Ideation",
                ["None", "Vague thoughts", "Specific thoughts", "Plan", "Intent"],
                "risk_assessment.homicidal_ideation", "risk_assessment"
            )
            
            violence_history = create_enhanced_selectbox(
                "History of Violence",
                ["None", "Verbal threats", "Property damage", "Physical violence", "Serious violence"],
                "risk_assessment.violence_history", "risk_assessment"
            )
        
        with col2:
            if homicidal_ideation != "None":
                target_identified = create_enhanced_selectbox(
                    "Target Identified",
                    ["No", "Yes - general", "Yes - specific person"],
                    "risk_assessment.target_identified", "risk_assessment"
                )
                
                if target_identified == "Yes - specific person":
                    st.warning("🚨 **TARASOFF WARNING**: Duty to warn may apply!")
                    
                    target_details = create_enhanced_text_area(
                        "Target Details (for clinical use)",
                        "risk_assessment.target_details", "risk_assessment",
                        height=80,
                        placeholder="Relationship to patient, location, contact info..."
                    )

def show_mse_section():
    """Mental State Examination with structured observations"""
    st.markdown("### 🧠 Mental State Examination")
    
    # Quick MSE template buttons
    st.markdown("**Quick Templates:**")
    mse_cols = st.columns(4)
    
    with mse_cols[0]:
        if st.button("📝 Normal MSE"):
            populate_normal_mse()
    with mse_cols[1]:
        if st.button("😔 Depressed MSE"):
            populate_depressed_mse()
    with mse_cols[2]:
        if st.button("😰 Anxious MSE"):
            populate_anxious_mse()
    with mse_cols[3]:
        if st.button("🌀 Psychotic MSE"):
            populate_psychotic_mse()
    
    tab1, tab2, tab3, tab4 = st.tabs(["Appearance & Behavior", "Speech & Mood", "Thought", "Cognition"])
    
    with tab1:
        col1, col2 = st.columns(2)
        
        with col1:
            appearance = st.multiselect(
                "Appearance",
                ["Well-groomed", "Unkempt", "Bizarre dress", "Age-appropriate", "Poor hygiene", "Unusual dress"],
                key="mse_appearance"
            )
            
            behavior = st.multiselect(
                "Behavior",
                ["Cooperative", "Agitated", "Withdrawn", "Hostile", "Restless", "Catatonic", "Bizarre"],
                key="mse_behavior"
            )
            
            eye_contact = create_enhanced_selectbox(
                "Eye Contact",
                ["Appropriate", "Avoidant", "Intense", "Darting", "Staring"],
                "mse.eye_contact", "mse"
            )
        
        with col2:
            psychomotor = create_enhanced_selectbox(
                "Psychomotor Activity",
                ["Normal", "Agitation", "Retardation", "Restlessness", "Stupor"],
                "mse.psychomotor", "mse"
            )
            
            attitude = create_enhanced_selectbox(
                "Attitude Toward Examiner", 
                ["Cooperative", "Guarded", "Hostile", "Seductive", "Demanding", "Indifferent"],
                "mse.attitude", "mse"
            )
    
    with tab2:
        col1, col2 = st.columns(2)
        
        with col1:
            speech_rate = create_enhanced_selectbox(
                "Speech Rate",
                ["Normal", "Slow", "Fast", "Pressured", "Pause-filled"],
                "mse.speech_rate", "mse"
            )
            
            speech_volume = create_enhanced_selectbox(
                "Speech Volume",
                ["Normal", "Loud", "Soft", "Whispered", "Variable"],
                "mse.speech_volume", "mse"
            )
            
            mood = create_enhanced_selectbox(
                "Stated Mood",
                ["Euthymic", "Depressed", "Anxious", "Irritable", "Euphoric", "Angry"],
                "mse.mood", "mse"
            )
        
        with col2:
            affect = st.multiselect(
                "Observed Affect",
                ["Appropriate", "Constricted", "Blunted", "Flat", "Labile", "Inappropriate"],
                key="mse_affect"
            )
            
            affect_congruence = create_enhanced_selectbox(
                "Mood-Affect Congruence",
                ["Congruent", "Incongruent", "Cannot assess"],
                "mse.affect_congruence", "mse"
            )
    
    with tab3:
        col1, col2 = st.columns(2)
        
        with col1:
            thought_process = st.multiselect(
                "Thought Process",
                ["Linear", "Tangential", "Circumstantial", "Flight of ideas", "Loose associations", 
                 "Thought blocking", "Perseveration"],
                key="mse_thought_process"
            )
            
            thought_content = st.multiselect(
                "Thought Content",
                ["No abnormalities", "Delusions", "Obsessions", "Compulsions", "Phobias", 
                 "Suicidal ideation", "Homicidal ideation"],
                key="mse_thought_content"
            )
        
        with col2:
            perceptions = st.multiselect(
                "Perceptual Abnormalities",
                ["None", "Auditory hallucinations", "Visual hallucinations", "Tactile hallucinations",
                 "Olfactory hallucinations", "Illusions"],
                key="mse_perceptions"
            )
            
            if "hallucinations" in str(perceptions):
                hallucination_details = create_enhanced_text_area(
                    "Hallucination Details",
                    "mse.hallucination_details", "mse",
                    height=80,
                    placeholder="Content, frequency, command nature..."
                )
    
    with tab4:
        col1, col2 = st.columns(2)
        
        with col1:
            orientation = create_enhanced_selectbox(
                "Orientation",
                ["Oriented x3", "Oriented x2", "Oriented x1", "Disoriented"],
                "mse.orientation", "mse"
            )
            
            attention = create_enhanced_selectbox(
                "Attention/Concentration",
                ["Intact", "Mildly impaired", "Moderately impaired", "Severely impaired"],
                "mse.attention", "mse"
            )
            
            memory = create_enhanced_selectbox(
                "Memory",
                ["Intact", "Recent impaired", "Remote impaired", "Both impaired"],
                "mse.memory", "mse"
            )
        
        with col2:
            abstract_thinking = create_enhanced_selectbox(
                "Abstract Thinking",
                ["Intact", "Concrete", "Impaired", "Cannot assess"],
                "mse.abstract_thinking", "mse"
            )
            
            insight = create_enhanced_selectbox(
                "Insight",
                ["Good", "Fair", "Poor", "Absent"],
                "mse.insight", "mse"
            )
            
            judgment = create_enhanced_selectbox(
                "Judgment", 
                ["Good", "Fair", "Poor", "Impaired"],
                "mse.judgment", "mse"
            )

def show_section_placeholder(section_name):
    """Placeholder for sections under development"""
    st.info(f"📝 **{section_name}** section is under development.")
    
    # Show basic form structure
    st.markdown("#### Available Fields:")
    
    section_key = section_name.lower().replace(' & ', '_').replace(' ', '_')
    
    # Add some basic fields for each section
    if "substance" in section_key:
        show_substance_use_section()
    elif "family" in section_key:
        show_family_history_section()
    elif "diagnostic" in section_key:
        show_diagnostic_formulation_section()
    elif "treatment" in section_key:
        show_treatment_planning_section()
    else:
        st.text_input("Sample field", placeholder=f"Enter {section_name.lower()} information...")
        st.text_area("Notes", placeholder=f"Additional notes for {section_name.lower()}...")

def show_substance_use_section():
    """Comprehensive substance use assessment"""
    st.markdown("### 🍷 Substance Use Assessment")
    
    # Screening questions
    with st.expander("📋 Screening Questions", expanded=True):
        col1, col2 = st.columns(2)
        
        with col1:
            alcohol_use = create_enhanced_selectbox(
                "Alcohol Use Pattern",
                ["Never", "Abstinent", "Occasional", "Regular", "Heavy", "Binge pattern"],
                "substance_use.alcohol_pattern", "substance_use"
            )
            
            if alcohol_use not in ["Never", "Abstinent"]:
                drinks_per_week = st.number_input(
                    "Drinks per week", 0, 100, 0,
                    help="Standard drinks (12oz beer, 5oz wine, 1.5oz spirits)",
                    key="drinks_per_week"
                )
                
                cage_score = show_cage_assessment()
        
        with col2:
            drug_use = st.multiselect(
                "Substances Used (lifetime)",
                ["Marijuana", "Cocaine", "Heroin", "Prescription opioids", "Methamphetamine", 
                 "MDMA", "Benzodiazepines", "Stimulants", "Hallucinogens"],
                key="substances_used"
            )
            
            if drug_use:
                current_use = create_enhanced_selectbox(
                    "Current Use Status",
                    ["Abstinent", "Occasional use", "Regular use", "Daily use", "Dependent"],
                    "substance_use.current_status", "substance_use"
                )
    
    # Detailed assessment for positive screens
    if alcohol_use not in ["Never", "Abstinent"] or drug_use:
        with st.expander("🔍 Detailed Assessment"):
            show_substance_detailed_assessment(alcohol_use, drug_use)

def show_cage_assessment():
    """CAGE questionnaire for alcohol screening"""
    st.markdown("**CAGE Screening:**")
    
    cage_questions = [
        "Have you ever felt you should Cut down on your drinking?",
        "Have people Annoyed you by criticizing your drinking?", 
        "Have you ever felt bad or Guilty about your drinking?",
        "Have you ever had a drink first thing in the morning (Eye-opener)?"
    ]
    
    cage_score = 0
    for i, question in enumerate(cage_questions):
        if st.checkbox(question, key=f"cage_{i}"):
            cage_score += 1
    
    if cage_score >= 2:
        st.warning(f"🚨 CAGE Score: {cage_score}/4 - Suggests possible alcohol problem")
    else:
        st.info(f"✅ CAGE Score: {cage_score}/4")
    
    return cage_score

def show_family_history_section():
    """Family history with genetic risk assessment"""
    st.markdown("### 👥 Family History")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### Psychiatric History")
        psychiatric_conditions = st.multiselect(
            "Family Psychiatric Conditions",
            ["Depression", "Bipolar disorder", "Anxiety disorders", "Schizophrenia", 
             "Substance use disorders", "Suicide", "Eating disorders", "ADHD"],
            key="family_psychiatric"
        )
        
        if psychiatric_conditions:
            for condition in psychiatric_conditions:
                relatives = st.multiselect(
                    f"Relatives with {condition}",
                    ["Mother", "Father", "Siblings", "Grandparents", "Aunts/Uncles", "Children"],
                    key=f"family_{condition.lower().replace(' ', '_')}"
                )
    
    with col2:
        st.markdown("#### Medical History")
        medical_conditions = st.multiselect(
            "Family Medical Conditions",
            ["Diabetes", "Hypertension", "Heart disease", "Cancer", "Neurological disorders",
             "Autoimmune disorders", "Thyroid disease", "Kidney disease"],
            key="family_medical"
        )
        
        genetic_testing = create_enhanced_selectbox(
            "Genetic Testing History",
            ["None", "Planned", "Completed - negative", "Completed - positive", "Declined"],
            "family_history.genetic_testing", "family_history"
        )

def show_diagnostic_formulation_section():
    """Diagnostic formulation with DSM-5-TR criteria"""
    st.markdown("### 🎯 Diagnostic Formulation")
    
    col1, col2 = st.columns(2)
    
    with col1:
        primary_diagnosis = create_enhanced_text_input(
            "Primary Diagnosis",
            "diagnostic.primary_diagnosis", "diagnostic_formulation",
            placeholder="e.g., Major Depressive Disorder, Single Episode, Moderate (F32.1)"
        )
        
        diagnostic_certainty = create_enhanced_selectbox(
            "Diagnostic Certainty",
            ["Definite", "Probable", "Possible", "Rule out", "Deferred"],
            "diagnostic.certainty", "diagnostic_formulation"
        )
        
        criteria_met = create_enhanced_text_area(
            "DSM-5-TR Criteria Met",
            "diagnostic.criteria_met", "diagnostic_formulation",
            height=120,
            placeholder="List specific criteria that are met..."
        )
    
    with col2:
        secondary_diagnoses = create_enhanced_text_area(
            "Secondary Diagnoses",
            "diagnostic.secondary_diagnoses", "diagnostic_formulation",
            height=80,
            placeholder="Additional psychiatric diagnoses..."
        )
        
        rule_out = create_enhanced_text_area(
            "Rule Out Diagnoses",
            "diagnostic.rule_out", "diagnostic_formulation",
            height=80,
            placeholder="Differential diagnoses to consider..."
        )
        
        specifiers = st.multiselect(
            "Specifiers",
            ["With anxious distress", "With mixed features", "With melancholic features",
             "With psychotic features", "With catatonia", "With seasonal pattern"],
            key="diagnostic_specifiers"
        )

def show_treatment_planning_section():
    """Comprehensive treatment planning"""
    st.markdown("### 📋 Treatment Planning")
    
    # Treatment goals
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### Treatment Goals")
        short_term_goals = create_enhanced_text_area(
            "Short-term Goals (1-3 months)",
            "treatment.short_term_goals", "treatment_planning",
            height=100,
            placeholder="Specific, measurable goals for the next 1-3 months..."
        )
        
        long_term_goals = create_enhanced_text_area(
            "Long-term Goals (6-12 months)",
            "treatment.long_term_goals", "treatment_planning",
            height=100,
            placeholder="Broader recovery and functional goals..."
        )
    
    with col2:
        st.markdown("#### Treatment Modalities")
        treatment_setting = create_enhanced_selectbox(
            "Treatment Setting",
            ["Outpatient", "Intensive outpatient", "Partial hospitalization", "Inpatient", "Residential"],
            "treatment.setting", "treatment_planning"
        )
        
        psychotherapy_type = st.multiselect(
            "Psychotherapy Recommendations",
            ["CBT", "DBT", "IPT", "Psychodynamic", "Family therapy", "Group therapy", "EMDR"],
            key="psychotherapy_types"
        )
        
        frequency = create_enhanced_selectbox(
            "Session Frequency",
            ["Weekly", "Twice weekly", "Biweekly", "Monthly", "As needed"],
            "treatment.frequency", "treatment_planning"
        )
    
    # Medication recommendations
    st.markdown("#### Medication Recommendations")
    medication_plan = create_enhanced_text_area(
        "Pharmacotherapy Plan",
        "treatment.medication_plan", "treatment_planning", 
        height=100,
        placeholder="Specific medications, dosing, monitoring requirements..."
    )

def show_navigation_controls(sections):
    """Enhanced navigation controls with keyboard shortcuts"""
    st.markdown("---")
    
    # Navigation buttons
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        if st.button("⬅️ Previous", key="nav_prev") and st.session_state.current_section > 0:
            st.session_state.current_section -= 1
            st.rerun()
    
    with col2:
        if st.button("💾 Save Progress", key="nav_save"):
            save_patient_to_db(is_manual=True)
    
    with col3:
        if st.button("✅ Validate", key="nav_validate"):
            validator.validate_patient_data(st.session_state.patient_data)
            if st.session_state.validation_errors:
                st.error(f"Found {len(st.session_state.validation_errors)} validation errors")
            else:
                st.success("✅ All validations passed!")
    
    with col4:
        if st.button("➡️ Next", key="nav_next") and st.session_state.current_section < len(sections) - 1:
            st.session_state.current_section += 1
            st.rerun()
    
    with col5:
        if st.button("🎯 Jump to Risk", key="nav_risk"):
            st.session_state.current_section = 10  # Risk Assessment
            st.rerun()
    
    # Keyboard shortcuts info
    if st.session_state.shortcuts_enabled:
        st.markdown("""
        **⌨️ Keyboard Shortcuts:**
        `Ctrl+S` Save • `Ctrl+←` Previous • `Ctrl+→` Next • `Ctrl+R` Risk Assessment
        """)
    
    # Progress summary
    completed_sections = len([k for k, v in st.session_state.patient_data.items() if v])
    progress_pct = (completed_sections / len(sections)) * 100
    
    st.progress(progress_pct / 100)
    st.caption(f"Progress: {completed_sections}/{len(sections)} sections completed ({progress_pct:.1f}%)")

# Helper functions for MSE templates
def populate_normal_mse():
    """Populate normal MSE findings"""
    mse_data = {
        'appearance': ['Well-groomed', 'Age-appropriate'],
        'behavior': ['Cooperative'],
        'speech_rate': 'Normal',
        'mood': 'Euthymic',
        'affect': 'Appropriate',
        'thought_process': 'Linear',
        'thought_content': 'No abnormalities',
        'perceptions': 'None',
        'orientation': 'Oriented x3',
        'attention': 'Intact',
        'memory': 'Intact',
        'insight': 'Good',
        'judgment': 'Good'
    }
    
    if 'mse' not in st.session_state.patient_data:
        st.session_state.patient_data['mse'] = {}
    
    st.session_state.patient_data['mse'].update(mse_data)
    mark_data_changed()
    st.success("✅ Normal MSE template applied!")
    st.rerun()

def populate_depressed_mse():
    """Populate depressed MSE findings"""
    mse_data = {
        'appearance': ['Well-groomed'],
        'behavior': ['Cooperative'],
        'speech_rate': 'Slow',
        'mood': 'Depressed',
        'affect': 'Constricted',
        'thought_process': 'Linear',
        'thought_content': 'No abnormalities',
        'perceptions': 'None',
        'psychomotor': 'Retardation',
        'eye_contact': 'Avoidant'
    }
    
    if 'mse' not in st.session_state.patient_data:
        st.session_state.patient_data['mse'] = {}
    
    st.session_state.patient_data['mse'].update(mse_data)
    mark_data_changed()
    st.success("✅ Depressed MSE template applied!")
    st.rerun()

def populate_anxious_mse():
    """Populate anxious MSE findings"""
    mse_data = {
        'appearance': ['Well-groomed'],
        'behavior': ['Cooperative', 'Restless'],
        'speech_rate': 'Fast',
        'mood': 'Anxious',
        'affect': 'Appropriate',
        'thought_process': 'Linear',
        'thought_content': 'No abnormalities',
        'perceptions': 'None',
        'psychomotor': 'Agitation'
    }
    
    if 'mse' not in st.session_state.patient_data:
        st.session_state.patient_data['mse'] = {}
    
    st.session_state.patient_data['mse'].update(mse_data)
    mark_data_changed()
    st.success("✅ Anxious MSE template applied!")
    st.rerun()

def populate_psychotic_mse():
    """Populate psychotic MSE findings"""
    mse_data = {
        'appearance': ['Unkempt'],
        'behavior': ['Withdrawn'],
        'speech_rate': 'Normal',
        'mood': 'Euthymic',
        'affect': 'Blunted',
        'thought_process': 'Loose associations',
        'thought_content': 'Delusions',
        'perceptions': 'Auditory hallucinations',
        'insight': 'Poor',
        'judgment': 'Poor'
    }
    
    if 'mse' not in st.session_state.patient_data:
        st.session_state.patient_data['mse'] = {}
    
    st.session_state.patient_data['mse'].update(mse_data)
    mark_data_changed()
    st.success("✅ Psychotic MSE template applied!")
    st.rerun()

def calculate_suicide_risk_level(current_si, means_access=None):
    """Calculate suicide risk level based on key factors"""
    risk_mapping = {
        'None': 'Low',
        'Passive (wish to be dead)': 'Low',
        'Active without plan': 'Moderate',
        'Active with plan': 'High',
        'Imminent risk': 'Imminent'
    }
    
    base_risk = risk_mapping.get(current_si, 'Low')
    
    # Escalate based on means access
    if means_access == 'Immediate access' and base_risk in ['Moderate', 'High']:
        return 'Imminent'
    elif means_access in ['Easy access', 'Immediate access'] and base_risk == 'Low':
        return 'Moderate'
    
    return base_risk

def generate_safety_plan_template(protective_factors, deterrents):
    """Generate a safety plan template"""
    template = """SAFETY PLAN

1. WARNING SIGNS:
- [List personal warning signs that crisis may be developing]

2. COPING STRATEGIES (things I can do on my own):
- [List healthy coping strategies that don't involve others]

3. SOCIAL CONTACTS/DISTRACTIONS:
- [List people and social settings that provide distraction]

4. PEOPLE I CAN ASK FOR HELP:
- [List trusted people who can be contacted during a crisis]

5. PROFESSIONALS/AGENCIES TO CONTACT:
- Therapist: _________________ Phone: _________________
- Crisis Line: National Suicide Prevention Lifeline 988
- Emergency: 911

6. MAKING THE ENVIRONMENT SAFE:
- [List ways to restrict access to lethal means]

PROTECTIVE FACTORS:"""
    
    if protective_factors:
        template += "\n- " + "\n- ".join(protective_factors)
    
    if deterrents:
        template += f"\n\nWHAT STOPS ME:\n- {deterrents}"
    
    return template

def show_substance_detailed_assessment(alcohol_use, drug_use):
    """Detailed substance use assessment"""
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### Usage Patterns")
        if alcohol_use not in ["Never", "Abstinent"]:
            alcohol_age_onset = st.number_input("Age first used alcohol", 0, 80, 18, key="alcohol_onset")
            last_drink = create_enhanced_selectbox(
                "Last Drink", 
                ["Today", "Yesterday", "This week", "This month", "Months ago"],
                "substance_use.last_drink", "substance_use"
            )
        
        if drug_use:
            primary_drug = create_enhanced_selectbox(
                "Primary Drug of Concern",
                drug_use + ["Other"],
                "substance_use.primary_drug", "substance_use"
            )
            
            drug_age_onset = st.number_input("Age first used drugs", 0, 80, 18, key="drug_onset")
            last_use = create_enhanced_selectbox(
                "Last Drug Use",
                ["Today", "Yesterday", "This week", "This month", "Months ago"],
                "substance_use.last_drug_use", "substance_use"
            )
    
    with col2:
        st.markdown("#### Impact & Consequences")
        consequences = st.multiselect(
            "Negative Consequences",
            ["Legal problems", "Work/school issues", "Relationship problems", "Health problems",
             "Financial problems", "Risky behavior", "Blackouts", "Withdrawal symptoms"],
            key="substance_consequences"
        )
        
        treatment_history = create_enhanced_selectbox(
            "Treatment History",
            ["None", "AA/NA meetings", "Outpatient counseling", "Inpatient treatment", 
             "Detox only", "Multiple treatments"],
            "substance_use.treatment_history", "substance_use"
        )
        
        if treatment_history not in ["None"]:
            treatment_details = create_enhanced_text_area(
                "Treatment Details",
                "substance_use.treatment_details", "substance_use",
                height=80,
                placeholder="Dates, locations, outcomes of previous treatments..."
            )

# Run the main application
if __name__ == "__main__":
    main()

# Enhanced analytics and reporting
def show_analytics_dashboard():
    """Show advanced analytics dashboard"""
    if not st.session_state.get('show_analytics', False):
        return
    
    st.markdown("---")
    st.markdown("### 📊 Assessment Analytics")
    
    # Performance metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        completion_pct = (st.session_state.required_fields_met / st.session_state.total_required_fields) * 100
        st.metric("Completion", f"{completion_pct:.1f}%", 
                 delta=f"{st.session_state.required_fields_met}/{st.session_state.total_required_fields}")
    
    with col2:
        assessment_duration = (datetime.now() - st.session_state.assessment_start_time).total_seconds() / 60
        st.metric("Duration", f"{assessment_duration:.1f}m")
    
    with col3:
        validation_score = max(0, 100 - len(st.session_state.validation_errors) * 10)
        st.metric("Data Quality", f"{validation_score}%", 
                 delta=f"-{len(st.session_state.validation_errors)} errors" if st.session_state.validation_errors else "No errors")
    
    with col4:
        st.metric("Render Performance", f"{st.session_state.last_render_time:.2f}s",
                 delta="Good" if st.session_state.last_render_time < st.session_state.load_time_threshold else "Slow")
    
    # Field completion heatmap
    if st.session_state.field_completion_status:
        st.markdown("#### Field Completion Status")
        
        # Create completion data
        completion_data = []
        for field_path, status in st.session_state.field_completion_status.items():
            section = field_path.split('.')[0]
            field = field_path.split('.')[-1]
            status_numeric = {'complete': 3, 'partial': 2, 'empty': 1, 'error': 0}.get(status, 0)
            completion_data.append({'Section': section, 'Field': field, 'Status': status_numeric})
        
        if completion_data:
            df = pd.DataFrame(completion_data)
            
            # Create pivot table for heatmap
            pivot_df = df.pivot(index='Field', columns='Section', values='Status').fillna(0)
            
            # Create heatmap
            fig = px.imshow(pivot_df, 
                          color_continuous_scale=['red', 'orange', 'yellow', 'green'],
                          title="Field Completion Heatmap",
                          labels={'color': 'Completion Status'},
                          height=400)
            
            fig.update_layout(
                xaxis_title="Sections",
                yaxis_title="Fields"
            )
            
            st.plotly_chart(fig, use_container_width=True)
    
    # Validation trend
    if st.session_state.validation_errors or st.session_state.validation_warnings:
        st.markdown("#### Validation Summary")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.session_state.validation_errors:
                st.error("**Errors:**")
                for error in st.session_state.validation_errors[:5]:
                    st.error(f"• {error}")
                if len(st.session_state.validation_errors) > 5:
                    st.error(f"... and {len(st.session_state.validation_errors) - 5} more")
        
        with col2:
            if st.session_state.validation_warnings:
                st.warning("**Warnings:**")
                for warning in st.session_state.validation_warnings[:5]:
                    st.warning(f"• {warning}")
                if len(st.session_state.validation_warnings) > 5:
                    st.warning(f"... and {len(st.session_state.validation_warnings) - 5} more")

# Enhanced footer with system status
def show_enhanced_footer():
    """Enhanced footer with comprehensive system status"""
    st.markdown("---")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown("**System Status**")
        st.markdown(f"🔢 Version: 2.1.0")
        st.markdown(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        
        db_status = "🟢 Online" if db and not st.session_state.offline_mode else "🔴 Offline"
        st.markdown(f"💾 Database: {db_status}")
        
        if st.session_state.offline_mode and st.session_state.pending_saves:
            st.markdown(f"📤 Pending: {len(st.session_state.pending_saves)} saves")
    
    with col2:
        st.markdown("**Performance**")
        st.markdown(f"⚡ Renders: {st.session_state.render_count}")
        st.markdown(f"⏱️ Avg Time: {st.session_state.last_render_time:.2f}s")
        
        auto_save_status = st.session_state.auto_save_status
        if "✅" in auto_save_status:
            st.markdown(f"💾 {auto_save_status}")
        elif "❌" in auto_save_status:
            st.markdown(f"💾 Save Error")
        else:
            st.markdown(f"💾 {auto_save_status}")
    
    with col3:
        st.markdown("**Data Quality**")
        completion_pct = (st.session_state.required_fields_met / st.session_state.total_required_fields) * 100
        st.markdown(f"📊 Complete: {completion_pct:.0f}%")
        st.markdown(f"✅ Fields: {st.session_state.required_fields_met}/{st.session_state.total_required_fields}")
        
        if st.session_state.validation_errors:
            st.markdown(f"⚠️ Errors: {len(st.session_state.validation_errors)}")
        else:
            st.markdown("✅ No Errors")
    
    with col4:
        st.markdown("**Support**")
        st.markdown("📞 Help: ext. 5555")
        st.markdown("📧 <EMAIL>")
        st.markdown("🔒 HIPAA Compliant")
        st.markdown("📖 [User Manual](https://docs.clinic.org)")

# Cleanup and exit handlers
def cleanup_on_exit():
    """Cleanup function called on app exit"""
    try:
        # Save any pending data
        if st.session_state.get('data_changed') and db and not st.session_state.offline_mode:
            enhanced_auto_save()
        
        # Close database connections
        if db:
            db.close_connections()
        
        logging.info("✅ App cleanup completed successfully")
    except Exception as e:
        logging.error(f"❌ Error during cleanup: {e}")

# Register cleanup function
atexit.register(cleanup_on_exit)

# Enhanced keyboard shortcut handler
def handle_keyboard_shortcuts():
    """Handle keyboard shortcuts for faster navigation"""
    if st.session_state.shortcuts_enabled:
        # This would be implemented with JavaScript in a real deployment
        # For now, we'll show the shortcuts in the UI
        shortcuts_html = """
        <script>
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                switch(e.key) {
                    case 's':
                        e.preventDefault();
                        // Trigger save
                        break;
                    case 'ArrowLeft':
                        e.preventDefault();
                        // Previous section
                        break;
                    case 'ArrowRight':
                        e.preventDefault();
                        // Next section
                        break;
                    case 'r':
                        e.preventDefault();
                        // Jump to risk assessment
                        break;
                }
            }
        });
        </script>
        """
        st.markdown(shortcuts_html, unsafe_allow_html=True)

# Final application execution
def run_enhanced_app():
    """Run the enhanced psychiatric assessment application"""
    try:
        # Initialize and validate session state
        initialize_session_state()
        
        # Handle keyboard shortcuts
        handle_keyboard_shortcuts()
        
        # Run main application
        main()
        
        # Show analytics if enabled
        show_analytics_dashboard()
        
        # Show enhanced footer
        show_enhanced_footer()
        
    except Exception as e:
        st.error(f"❌ Application Error: {str(e)}")
        
        if st.session_state.debug:
            st.markdown("### 🐛 Debug Information")
            st.exception(e)
            
            # Show session state for debugging
            with st.expander("Session State"):
                st.json({k: str(v) for k, v in st.session_state.items()})
        
        # Offer recovery options
        st.markdown("### 🔧 Recovery Options")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🔄 Reload App"):
                st.cache_resource.clear()
                st.cache_data.clear()
                st.rerun()
        
        with col2:
            if st.button("💾 Emergency Save"):
                try:
                    export_data = prepare_export_data()
                    st.download_button(
                        "📥 Download Emergency Backup",
                        data=json.dumps(export_data, indent=2, default=str),
                        file_name=f"emergency_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                        mime="application/json"
                    )
                except Exception as save_error:
                    st.error(f"Emergency save failed: {str(save_error)}")
        
        with col3:
            if st.button("🆘 Report Issue"):
                st.info("Please contact support at ext. 5555 or <EMAIL>")
        
        logging.error(f"Application error: {e}", exc_info=True)

# Execute the application
if __name__ == "__main__":
    run_enhanced_app()